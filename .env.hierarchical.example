# 层次化RAG配置示例
# 复制此文件为 .env.hierarchical 并根据需要修改配置

# ================================
# 层次化RAG基本配置
# ================================

# 是否启用层次化RAG处理
ENABLE_HIERARCHICAL_RAG=true

# 父段落模式: paragraph (段落模式) 或 full_doc (全文档模式)
HIERARCHICAL_PARENT_MODE=paragraph

# 父段落配置
HIERARCHICAL_PARENT_CHUNK_SIZE=1000
HIERARCHICAL_PARENT_CHUNK_OVERLAP=100

# 子块配置
HIERARCHICAL_CHILD_CHUNK_SIZE=300
HIERARCHICAL_CHILD_CHUNK_OVERLAP=50

# 索引配置
# 是否仅将子块索引到向量数据库 (推荐设置为true)
HIERARCHICAL_INDEX_CHILD_ONLY=true

# 是否启用父段落上下文 (推荐设置为true)
HIERARCHICAL_ENABLE_PARENT_CONTEXT=true

# ================================
# 质量控制配置
# ================================

# 最小子块大小
HIERARCHICAL_MIN_CHILD_CHUNK_SIZE=50

# 每个父段落的最大子块数量
HIERARCHICAL_MAX_CHILDREN_PER_PARENT=20

# 最小父段落大小
HIERARCHICAL_MIN_PARENT_CHUNK_SIZE=200

# ================================
# 内容分析配置
# ================================

# 是否启用内容分析
HIERARCHICAL_ENABLE_CONTENT_ANALYSIS=true

# 默认内容类型 (可选值: academic, news, dialogue, code, legal)
# 留空表示自动检测
HIERARCHICAL_DEFAULT_CONTENT_TYPE=

# ================================
# 数据库配置
# ================================

# MongoDB集合名称
HIERARCHICAL_SEGMENTS_COLLECTION=document_segments
HIERARCHICAL_CHUNKS_COLLECTION=child_chunks

# ================================
# 性能配置
# ================================

# 批处理大小
HIERARCHICAL_BATCH_SIZE=50

# 处理超时时间 (秒)
HIERARCHICAL_PROCESSING_TIMEOUT=1800

# 并发处理数量
HIERARCHICAL_MAX_CONCURRENT_PROCESSES=4

# ================================
# 搜索配置
# ================================

# 默认搜索结果数量
HIERARCHICAL_DEFAULT_TOP_K=5

# 默认分数阈值
HIERARCHICAL_DEFAULT_SCORE_THRESHOLD=0.0

# 是否默认启用父段落上下文
HIERARCHICAL_DEFAULT_ENABLE_PARENT_CONTEXT=true

# ================================
# 日志配置
# ================================

# 层次化处理的日志级别
HIERARCHICAL_LOG_LEVEL=INFO

# 是否启用详细的调试日志
HIERARCHICAL_DEBUG_LOGGING=false

# ================================
# 缓存配置
# ================================

# 是否启用内容分析缓存
HIERARCHICAL_ENABLE_CONTENT_CACHE=true

# 缓存过期时间 (秒)
HIERARCHICAL_CACHE_EXPIRE_TIME=3600

# ================================
# 向量存储配置
# ================================

# 层次化向量集合名称
HIERARCHICAL_VECTOR_COLLECTION=hierarchical_rag_documents

# 向量维度 (通常由嵌入模型决定)
HIERARCHICAL_VECTOR_DIMENSION=1536

# ================================
# 高级配置
# ================================

# 是否启用智能分割
HIERARCHICAL_ENABLE_SMART_SPLITTING=true

# 是否保持语义边界
HIERARCHICAL_PRESERVE_SEMANTIC_BOUNDARIES=true

# 质量阈值
HIERARCHICAL_QUALITY_THRESHOLD=0.8

# 最大块大小比例
HIERARCHICAL_MAX_CHUNK_SIZE_RATIO=1.5