# 前端应用配置文件
# 生产环境配置

# ==================== API配置 ====================
# 生产环境使用完整URL
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_API_VERSION=/api/v1

# ==================== 认证配置 ====================
VITE_TOKEN_STORAGE_KEY=rag_chat_token
VITE_USER_STORAGE_KEY=rag_chat_user

# ==================== UI配置 ====================
VITE_APP_NAME=RAG Chat
VITE_DEFAULT_THEME=light
VITE_DEFAULT_LOCALE=zh-CN

# ==================== 上传配置 ====================
VITE_MAX_UPLOAD_SIZE=10485760  # 10MB
VITE_ALLOWED_FILE_TYPES=pdf,docx,txt,md

# ==================== 聊天配置 ====================
VITE_MAX_CHAT_HISTORY=100
VITE_MESSAGE_POLL_INTERVAL=1000  # 1秒

# ==================== 环境特定配置 ====================
# 生产环境
VITE_ENABLE_DEBUG=false
VITE_ENABLE_MOCK=false
