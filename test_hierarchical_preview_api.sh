#!/bin/bash

# 测试层级预览API的curl命令
# 使用方法: bash test_hierarchical_preview_api.sh

echo "=== 文档切割层级预览API测试命令 ==="
echo ""

# 设置变量
BASE_URL="http://localhost:8000"
TOKEN="your_auth_token_here"
DOCUMENT_ID="your_document_id_here"

echo "1. 获取已上传文档的完整层级预览："
echo "curl -X GET \\"
echo "  \"${BASE_URL}/api/v1/rag/collections/${DOCUMENT_ID}/complete-preview\" \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\" \\"
echo "  -H \"Content-Type: application/json\""
echo ""

echo "2. 获取预览模式文档的完整层级预览："
PREVIEW_DOC_ID="preview_20250704_214542_example"
echo "curl -X GET \\"
echo "  \"${BASE_URL}/api/v1/rag/collections/${PREVIEW_DOC_ID}/complete-preview\" \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\" \\"
echo "  -H \"Content-Type: application/json\""
echo ""

echo "3. 上传文档并生成预览数据："
echo "curl -X POST \\"
echo "  \"${BASE_URL}/api/v1/rag/documents/upload\" \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\" \\"
echo "  -F \"file=@/path/to/your/document.txt\" \\"
echo "  -F \"parent_chunk_size=512\" \\"
echo "  -F \"parent_chunk_overlap=50\" \\"
echo "  -F \"parent_separator=\\\\n\\\\n\" \\"
echo "  -F \"child_chunk_size=256\" \\"
echo "  -F \"child_chunk_overlap=12\" \\"
echo "  -F \"child_separator=\\\\n\" \\"
echo "  -F \"preview_only=true\" \\"
echo "  -F \"processor_type=parent_child\" \\"
echo "  -F \"parent_mode=paragraph\""
echo ""

echo "4. 获取文档列表："
echo "curl -X GET \\"
echo "  \"${BASE_URL}/api/v1/rag/documents\" \\"
echo "  -H \"Authorization: Bearer ${TOKEN}\" \\"
echo "  -H \"Content-Type: application/json\""
echo ""

echo "=== 使用说明 ==="
echo "1. 请将 'your_auth_token_here' 替换为实际的认证令牌"
echo "2. 请将 'your_document_id_here' 替换为实际的文档ID"
echo "3. 请将 '/path/to/your/document.txt' 替换为实际的文档路径"
echo "4. 预览模式文档ID格式通常为: preview_YYYYMMDD_HHMMSS_filename"
echo ""

echo "=== 测试步骤 ==="
echo "1. 首先运行第4个命令获取文档列表，找到要测试的文档ID"
echo "2. 使用第1个命令测试已上传文档的层级预览"
echo "3. 或者使用第3个命令上传新文档并获得预览ID"
echo "4. 然后使用第2个命令测试预览模式文档的层级预览"
echo ""

echo "=== 期望的响应格式 ==="
echo "{"
echo "  \"success\": true,"
echo "  \"message\": \"获取完整文档预览成功\","
echo "  \"preview_mode\": false,"
echo "  \"doc_id\": \"document_id\","
echo "  \"total_segments\": 20,"
echo "  \"parent_segments\": 6,"
echo "  \"child_segments\": 14,"
echo "  \"segments\": ["
echo "    {"
echo "      \"type\": \"parent\","
echo "      \"id\": \"parent_0\","
echo "      \"content\": \"父块内容...\","
echo "      \"metadata\": {...},"
echo "      \"children\": ["
echo "        {"
echo "          \"type\": \"child\","
echo "          \"id\": \"child_0_0\","
echo "          \"content\": \"子块内容...\","
echo "          \"metadata\": {...},"
echo "          \"parent_id\": \"parent_0\""
echo "        }"
echo "      ]"
echo "    }"
echo "  ]"
echo "}"